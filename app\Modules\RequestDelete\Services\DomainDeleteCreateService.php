<?php

namespace App\Modules\RequestDelete\Services;

use App\Mail\UserDeleteRequestMail;
use App\Modules\Client\Constants\DomainStatus;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;

class DomainDeleteCreateService
{
    private Carbon $now;

    public function __construct()
    {
        $this->now = Carbon::now();
    }

    public static function instance()
    {
        return new self;
    }

    public function createDeleteRequestSave($request)
    {
        $data = $request->all();

        // Validate that domain can have a new request
        $this->validateDomainCanHaveNewRequest($data['domainId']);

        $this->processCreateRequest($data);
    }

    private function validateDomainCanHaveNewRequest($domainId): void
    {
        $existingRequest = DB::client()->table('domain_cancellation_requests')
            ->join('domains', 'domain_cancellation_requests.domain_id', '=', 'domains.id')
            ->where('domain_cancellation_requests.domain_id', $domainId)
            ->select([
                'domain_cancellation_requests.deleted_at',
                'domain_cancellation_requests.feedback_date',
                'domain_cancellation_requests.support_note',
                'domains.status as domain_status'
            ])
            ->first();

        if (!$existingRequest) {
            return; // No existing request, can create new one
        }

        // Check if existing request is in PENDING or APPROVED status
        $isPending = is_null($existingRequest->deleted_at) && is_null($existingRequest->feedback_date);
        $isApproved = !is_null($existingRequest->deleted_at) && !is_null($existingRequest->feedback_date) && $existingRequest->domain_status === 'IN_PROCESS';

        if ($isPending || $isApproved) {
            throw new \Exception("A domain deletion request already exists for this domain. Only domains with REJECTED or CANCELLED requests can have new deletion requests.");
        }

        // Check if it's REJECTED or CANCELLED (these can have new requests)
        $isRejected = is_null($existingRequest->deleted_at) && !is_null($existingRequest->feedback_date) && $existingRequest->domain_status === 'ACTIVE' && !str_contains($existingRequest->support_note ?? '', 'CANCELLED:');
        $isCancelled = is_null($existingRequest->deleted_at) && !is_null($existingRequest->feedback_date) && $existingRequest->domain_status === 'ACTIVE' && str_contains($existingRequest->support_note ?? '', 'CANCELLED:');

        if (!$isRejected && !$isCancelled) {
            throw new \Exception("Cannot create new deletion request. Domain must be in REJECTED or CANCELLED status to allow new requests.");
        }
    }

    private function processCreateRequest(array $data): void
    {
        $adminId = Auth::id();
        $adminName = Auth::user()->name ?? 'System';
        $adminEmail = Auth::user()->email ?? '<EMAIL>';
        $supportNote = "Request delete created by {$adminEmail}";

        // Update domain status to IN_PROCESS (like client-side does)
        $this->updateDomainStatusDeletion($data['domainId']);

        // Create the deletion request (already approved by admin)
        $this->createDeletionRequest($data, $adminId, $adminName, $supportNote);

        // Send notifications to the domain owner
        $this->userNotification($data);
        $this->userEmailNotification($data);
    }

    private function userNotification($requestData)
    {
        $userID = $requestData['userID'];
        $domainName = $requestData['domainName'];

        if (!$userID || !$domainName) return;

        $message = 'A deletion request for your domain "' . $domainName . '" has been created by admin and approved. The deletion process will take 1-2 days to complete.';

        DB::client()->table('notifications')->insert([
            'user_id'      => $userID,
            'title'        => 'Domain Deletion Request Created',
            'message'      => $message,
            'redirect_url' => '/domain',
            'created_at'   => now(),
            'importance'   => 'important',
        ]);
    }

    private function userEmailNotification($requestData)
    {
        $domainName = $requestData['domainName'];
        $userEmail = $requestData['userEmail'];

        $body = 'A deletion request for your domain "' . $domainName . '" has been created by admin and approved. The deletion process will take 1-2 days to complete. This action is final and cannot be undone.';

        $message = [
            'subject'  => 'Domain Deletion Request Created',
            'greeting' => 'Greetings!',
            'body'     => $body,
            'text'     => Carbon::now()->format('Y-m-d H:i:s'),
            'sender'   => 'StrangeDomains Support',
        ];

        Mail::to($userEmail)->send(new UserDeleteRequestMail($message));

        $this->emailTrack($userEmail, $message, $requestData['domainId']);
    }

    private function getUserByDomain($domainId) 
    {
        return DB::client()->table('registered_domains')
            ->select('users.id as user_id', 'users.email', 'users.first_name', 'users.last_name')
            ->join('user_contacts', 'user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
            ->join('users', 'users.id', '=', 'user_contacts.user_id')
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->where('domains.id', $domainId)
            ->first();
    }

    private function emailTrack($email, array $payload, $domainId = null) 
    {
        $userId = null;
        $userName = null;

        if ($domainId) {
            $user = $this->getUserByDomain($domainId);
            if ($user) {
                $userId = $user->user_id;
                $userName = $user->first_name . ' ' . $user->last_name;
            }
        }

        $emailSent = DB::client()->table('email_histories')
            ->insert([
                'user_id' => $userId,
                'name' => $userName ?? 'System',
                'recipient_email' => $email,
                'subject' => 'Domain Deletion Request Created and Approved',
                'email_type' => 'Domain Deletion Request Created and Approved',
                'email_body' => json_encode($payload),
                'attachment' => null,
                'created_at' => now(),
                'updated_at' => now()
            ]);

        return $emailSent;
    }

    private function createDeletionRequest(array $data, int $adminId, string $adminName, string $supportNote): void
    {
        $this->createDomainCancellationRequest($data, $adminId, $adminName, $supportNote);
    }

    private function createDomainCancellationRequest(array $data, int $adminId, string $adminName, string $supportNote): void
    {
        $adminEmail = Auth::user()->email ?? '<EMAIL>';
        $adminFullName = "{$adminName} ({$adminEmail})";

        // Admin creates request that's already approved - ready for cron job processing
        DB::client()->table('domain_cancellation_requests')->insert([
            'user_id'             => $data['userID'],
            'domain_id'           => $data['domainId'],
            'reason'              => $data['reason'] ?? 'Domain deletion request by ' . $adminFullName,
            'requested_at'        => now(),
            'support_agent_id'    => $adminId,
            'support_agent_name'  => $adminFullName,
            'feedback_date'       => now(),
            'support_note'        => $supportNote,
            'deleted_at'          => now(), // Mark as approved - ready for cron job
            'is_refunded'         => false,
            // 'created_at'          => now(),
            // 'updated_at'          => now(),
        ]);
    }

    private function updateDomainStatusDeletion($domainId): void
    {
        DB::client()->table('domains')
            ->where('id', $domainId)
            ->update([
                'status' => DomainStatus::IN_PROCESS,
                'updated_at' => now(),
            ]);
    }
}
